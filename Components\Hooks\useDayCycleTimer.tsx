import { useState, useEffect, useRef, useCallback } from 'react';

// 🌅 TEMPORISATEUR DE JOURNÉE RÉVOLUTIONNAIRE
// Un seul bouton pour contrôler un cycle complet jour/nuit automatique

// Types pour le cycle de journée
type DayPhase = 'night' | 'dawn' | 'sunrise' | 'morning' | 'midday' | 'afternoon' | 'sunset' | 'dusk';
type CycleStatus = 'stopped' | 'running' | 'paused';

interface DayCycleState {
  currentPhase: DayPhase;
  phaseIndex: number;
  progress: number; // 0-1 progression dans le cycle complet
  phaseProgress: number; // 0-1 progression dans la phase actuelle
  elapsedTime: number;
  cycleDuration: number; // Durée totale du cycle en ms
  status: CycleStatus;
}

interface UseDayCycleTimerProps {
  onPhaseChange?: (phase: DayPhase, phaseIndex: number) => void;
  onCycleComplete?: () => void;
  initialCycleDuration?: number; // En minutes
  autoRestart?: boolean; // Redémarrer automatiquement après un cycle complet
  initialPhase?: DayPhase; // 🔧 CISCO: Phase de démarrage (pour synchronisation)
}

// 🌍 PHASES DU CYCLE (dans l'ordre chronologique)
const DAY_PHASES: DayPhase[] = [
  'night',      // 0 - Nuit profonde (point de départ)
  'dawn',       // 1 - Aube
  'sunrise',    // 2 - Lever du soleil
  'morning',    // 3 - Matin
  'midday',     // 4 - Midi (zénith)
  'afternoon',  // 5 - Après-midi
  'sunset',     // 6 - Coucher du soleil
  'dusk'        // 7 - Crépuscule
];

// 🎨 ÉMOJIS POUR CHAQUE PHASE
const PHASE_EMOJIS: Record<DayPhase, string> = {
  night: '🌌',
  dawn: '🌅',
  sunrise: '🌄',
  morning: '🌞',
  midday: '☀️',
  afternoon: '🌇',
  sunset: '🌆',
  dusk: '🌃'
};

// 🎯 HOOK PRINCIPAL
export const useDayCycleTimer = ({
  onPhaseChange,
  onCycleComplete,
  initialCycleDuration = 8, // 8 minutes par défaut
  autoRestart = true,
  initialPhase = 'midday' // 🔧 CISCO: Démarrage sur zénith pour cohérence
}: UseDayCycleTimerProps = {}) => {

  // 🔧 CISCO: Calcul de l'index de la phase initiale
  const initialPhaseIndex = DAY_PHASES.indexOf(initialPhase);

  // 📊 État du cycle
  const [state, setState] = useState<DayCycleState>({
    currentPhase: initialPhase, // 🔧 CISCO: Phase initiale synchronisée
    phaseIndex: initialPhaseIndex, // 🔧 CISCO: Index correspondant
    progress: initialPhaseIndex / DAY_PHASES.length, // 🔧 CISCO: Progression initiale
    phaseProgress: 0,
    elapsedTime: 0,
    cycleDuration: initialCycleDuration * 60 * 1000, // Conversion en ms
    status: 'stopped'
  });

  // 🔄 Références pour le timer
  const intervalRef = useRef<number | null>(null);
  const startTimeRef = useRef<number>(0);
  const pausedTimeRef = useRef<number>(0);
  const lastPhaseRef = useRef<number>(initialPhaseIndex); // 🔧 CISCO: Initialiser avec la phase de départ

  // 🧮 CALCULS DES PHASES
  const getPhaseDuration = useCallback(() => {
    return state.cycleDuration / DAY_PHASES.length; // Durée égale pour chaque phase
  }, [state.cycleDuration]);

  const getCurrentPhaseFromTime = useCallback((elapsedTime: number) => {
    const phaseDuration = getPhaseDuration();
    const phaseIndex = Math.floor(elapsedTime / phaseDuration) % DAY_PHASES.length;
    const phaseProgress = (elapsedTime % phaseDuration) / phaseDuration;
    const totalProgress = (elapsedTime % state.cycleDuration) / state.cycleDuration;
    
    return {
      phaseIndex,
      currentPhase: DAY_PHASES[phaseIndex],
      phaseProgress,
      totalProgress
    };
  }, [getPhaseDuration, state.cycleDuration]);

  // 🔄 FONCTION DE MISE À JOUR DU TIMER
  const updateTimer = useCallback(() => {
    if (state.status !== 'running') return;

    const now = Date.now();
    const elapsedTime = now - startTimeRef.current;
    const phaseInfo = getCurrentPhaseFromTime(elapsedTime);

    setState(prev => ({
      ...prev,
      elapsedTime,
      currentPhase: phaseInfo.currentPhase,
      phaseIndex: phaseInfo.phaseIndex,
      progress: phaseInfo.totalProgress,
      phaseProgress: phaseInfo.phaseProgress
    }));

    // 🎯 DÉTECTION CHANGEMENT DE PHASE
    if (phaseInfo.phaseIndex !== lastPhaseRef.current) {
      console.log(`🌅 CHANGEMENT DE PHASE: ${DAY_PHASES[lastPhaseRef.current]} → ${phaseInfo.currentPhase} (${phaseInfo.phaseIndex + 1}/8)`);
      
      if (onPhaseChange) {
        onPhaseChange(phaseInfo.currentPhase, phaseInfo.phaseIndex);
      }
      
      lastPhaseRef.current = phaseInfo.phaseIndex;
    }

    // 🔄 DÉTECTION FIN DE CYCLE
    if (elapsedTime >= state.cycleDuration) {
      console.log('🎉 CYCLE COMPLET TERMINÉ !');
      
      if (onCycleComplete) {
        onCycleComplete();
      }

      if (autoRestart) {
        // Redémarrer automatiquement
        console.log('🔄 REDÉMARRAGE AUTOMATIQUE DU CYCLE');
        startTimeRef.current = now;
        lastPhaseRef.current = 0;
      } else {
        // Arrêter le cycle
        stop();
      }
    }
  }, [state.status, state.cycleDuration, getCurrentPhaseFromTime, onPhaseChange, onCycleComplete, autoRestart]);

  // ⚡ DÉMARRAGE DU TIMER
  useEffect(() => {
    if (state.status === 'running') {
      intervalRef.current = window.setInterval(updateTimer, 100); // Mise à jour toutes les 100ms
      return () => {
        if (intervalRef.current) {
          clearInterval(intervalRef.current);
          intervalRef.current = null;
        }
      };
    }
  }, [state.status, updateTimer]);

  // 🎮 CONTRÔLES DU CYCLE

  // ▶️ DÉMARRER
  const start = useCallback(() => {
    console.log('🚀 DÉMARRAGE DU CYCLE DE JOURNÉE');
    startTimeRef.current = Date.now() - state.elapsedTime;
    lastPhaseRef.current = state.phaseIndex;
    
    setState(prev => ({
      ...prev,
      status: 'running'
    }));
  }, [state.elapsedTime, state.phaseIndex]);

  // ⏸️ PAUSE
  const pause = useCallback(() => {
    console.log('⏸️ PAUSE DU CYCLE');
    pausedTimeRef.current = Date.now();
    
    setState(prev => ({
      ...prev,
      status: 'paused'
    }));
  }, []);

  // ▶️ REPRENDRE
  const resume = useCallback(() => {
    console.log('▶️ REPRISE DU CYCLE');
    const pauseDuration = Date.now() - pausedTimeRef.current;
    startTimeRef.current += pauseDuration;
    
    setState(prev => ({
      ...prev,
      status: 'running'
    }));
  }, []);

  // ⏹️ ARRÊTER
  const stop = useCallback(() => {
    console.log('⏹️ ARRÊT DU CYCLE');
    
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
      intervalRef.current = null;
    }
    
    setState(prev => ({
      ...prev,
      status: 'stopped'
    }));
  }, []);

  // 🔄 RESET
  const reset = useCallback(() => {
    console.log('🔄 RESET DU CYCLE');

    if (intervalRef.current) {
      clearInterval(intervalRef.current);
      intervalRef.current = null;
    }

    setState(prev => ({
      ...prev,
      currentPhase: initialPhase, // 🔧 CISCO: Reset vers phase initiale (midday)
      phaseIndex: initialPhaseIndex, // 🔧 CISCO: Index correspondant
      progress: initialPhaseIndex / DAY_PHASES.length, // 🔧 CISCO: Progression correspondante
      phaseProgress: 0,
      elapsedTime: 0,
      status: 'stopped'
    }));

    lastPhaseRef.current = initialPhaseIndex; // 🔧 CISCO: Reset vers phase initiale
  }, [initialPhase, initialPhaseIndex]);

  // ⚙️ CHANGER LA DURÉE DU CYCLE
  const setCycleDuration = useCallback((durationMinutes: number) => {
    const newDurationMs = durationMinutes * 60 * 1000;
    console.log(`⚙️ NOUVELLE DURÉE DE CYCLE: ${durationMinutes} minutes`);
    
    setState(prev => ({
      ...prev,
      cycleDuration: newDurationMs
    }));
  }, []);

  // 🎯 ALLER DIRECTEMENT À UNE PHASE
  const goToPhase = useCallback((targetPhase: DayPhase) => {
    const phaseIndex = DAY_PHASES.indexOf(targetPhase);
    if (phaseIndex === -1) return;
    
    const phaseDuration = getPhaseDuration();
    const targetTime = phaseIndex * phaseDuration;
    
    console.log(`🎯 SAUT VERS LA PHASE: ${targetPhase} (${phaseIndex + 1}/8)`);
    
    startTimeRef.current = Date.now() - targetTime;
    lastPhaseRef.current = phaseIndex;
    
    setState(prev => ({
      ...prev,
      currentPhase: targetPhase,
      phaseIndex,
      elapsedTime: targetTime,
      progress: targetTime / prev.cycleDuration,
      phaseProgress: 0
    }));
    
    if (onPhaseChange) {
      onPhaseChange(targetPhase, phaseIndex);
    }
  }, [getPhaseDuration, onPhaseChange]);

  // 🧹 NETTOYAGE
  useEffect(() => {
    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, []);

  // 📤 RETOUR DES DONNÉES ET CONTRÔLES
  return {
    // 📊 État actuel
    currentPhase: state.currentPhase,
    phaseIndex: state.phaseIndex,
    progress: state.progress,
    phaseProgress: state.phaseProgress,
    elapsedTime: state.elapsedTime,
    cycleDuration: state.cycleDuration,
    status: state.status,
    
    // 🎨 Utilitaires
    currentPhaseEmoji: PHASE_EMOJIS[state.currentPhase],
    phaseName: state.currentPhase,
    phaseNumber: state.phaseIndex + 1,
    totalPhases: DAY_PHASES.length,
    phaseDuration: getPhaseDuration(),
    
    // 🎮 Contrôles
    start,
    pause,
    resume,
    stop,
    reset,
    setCycleDuration,
    goToPhase,
    
    // 📋 Données de référence
    allPhases: DAY_PHASES,
    phaseEmojis: PHASE_EMOJIS,
    
    // 🔧 État technique
    isRunning: state.status === 'running',
    isPaused: state.status === 'paused',
    isStopped: state.status === 'stopped'
  };
};

export default useDayCycleTimer;
